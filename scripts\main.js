import { world, system } from "@minecraft/server";

const interval = 150; // seconds (2.5 minutes - adjust to 300 for 5 minutes)
const warningTime = 10;
let countdown = interval;

// List of all hostile mob IDs
const hostileMobs = new Set([
  "minecraft:zombie",
  "minecraft:husk",
  "minecraft:drowned",
  "minecraft:skeleton",
  "minecraft:stray",
  "minecraft:spider",
  "minecraft:cave_spider",
  "minecraft:creeper",
  "minecraft:enderman",
  "minecraft:witch",
  "minecraft:vindicator",
  "minecraft:evoker",
  "minecraft:ravager",
  "minecraft:pillager",
  "minecraft:illusioner",
  "minecraft:zombified_piglin",
  "minecraft:blaze",
  "minecraft:ghast",
  "minecraft:magma_cube",
  "minecraft:slime",
  "minecraft:phantom",
  "minecraft:warden",
  "minecraft:shulker",
  "minecraft:guardian",
  "minecraft:elder_guardian",
  "minecraft:hoglin",
  "minecraft:piglin_brute"
]);

const dimensions = ["overworld", "nether", "the_end"];

world.afterEvents.worldInitialize.subscribe(() => {
  console.warn("✅ ClearLag system started");

  system.runInterval(() => {
    countdown--;

    // 10 second warning
    if (countdown === warningTime) {
      for (const player of world.getPlayers()) {
        player.sendMessage("⚠️ Clearing dropped items and hostile mobs in 10 seconds (all dimensions)!");
      }
    }

    // Perform clear
    if (countdown <= 0) {
      let removedItems = 0;
      let removedMobs = 0;

      for (const dimName of dimensions) {
        const dimension = world.getDimension(dimName);

        for (const entity of dimension.getEntities()) {
          if (entity.typeId === "minecraft:item") {
            entity.kill();
            removedItems++;
          } else if (hostileMobs.has(entity.typeId)) {
            entity.kill();
            removedMobs++;
          }
        }
      }

      for (const player of world.getPlayers()) {
        player.sendMessage(`✅ Cleared ${removedItems} dropped items and ${removedMobs} hostile mobs from all dimensions.`);
      }

      countdown = interval;
    }
  }, 20); // Runs every second
});
