import { world, system } from "@minecraft/server";

const checkInterval = 10; // Check every 10 seconds
const itemMaxAge = 300; // 5 minutes in seconds
const warningTime = 10; // Warning time in seconds before clearing
let countdown = checkInterval;

// Map to store item spawn timestamps
const itemTimestamps = new Map();

// List of all hostile mob IDs
const hostileMobs = new Set([
  "minecraft:zombie",
  "minecraft:husk",
  "minecraft:drowned",
  "minecraft:skeleton",
  "minecraft:stray",
  "minecraft:spider",
  "minecraft:cave_spider",
  "minecraft:creeper",
  "minecraft:enderman",
  "minecraft:witch",
  "minecraft:vindicator",
  "minecraft:evoker",
  "minecraft:ravager",
  "minecraft:pillager",
  "minecraft:illusioner",
  "minecraft:zombified_piglin",
  "minecraft:blaze",
  "minecraft:ghast",
  "minecraft:magma_cube",
  "minecraft:slime",
  "minecraft:phantom",
  "minecraft:warden",
  "minecraft:shulker",
  "minecraft:guardian",
  "minecraft:elder_guardian",
  "minecraft:hoglin",
  "minecraft:piglin_brute"
]);

const dimensions = ["overworld", "nether", "the_end"];

// Track when items are spawned
world.afterEvents.entitySpawn.subscribe((event) => {
  if (event.entity.typeId === "minecraft:item") {
    const currentTime = Date.now();
    itemTimestamps.set(event.entity.id, currentTime);
  }
});

// Clean up timestamps when items are removed
world.afterEvents.entityRemove.subscribe((event) => {
  if (event.removedEntityId && itemTimestamps.has(event.removedEntityId)) {
    itemTimestamps.delete(event.removedEntityId);
  }
});

world.afterEvents.worldInitialize.subscribe(() => {
  console.warn("✅ ClearLag system started - Items older than 5 minutes will be cleared every 10 seconds");

  system.runInterval(() => {
    countdown--;

    // Check and clear old items every 10 seconds
    if (countdown <= 0) {
      let removedItems = 0;
      let removedMobs = 0;
      const currentTime = Date.now();
      const itemsToRemove = [];

      for (const dimName of dimensions) {
        const dimension = world.getDimension(dimName);

        for (const entity of dimension.getEntities()) {
          if (entity.typeId === "minecraft:item") {
            const spawnTime = itemTimestamps.get(entity.id);

            // If we don't have a timestamp (item existed before addon loaded) or item is older than 5 minutes
            if (!spawnTime || (currentTime - spawnTime) >= (itemMaxAge * 1000)) {
              entity.kill();
              removedItems++;
              itemsToRemove.push(entity.id);
            }
          } else if (hostileMobs.has(entity.typeId)) {
            entity.kill();
            removedMobs++;
          }
        }
      }

      // Clean up timestamps for removed items
      for (const itemId of itemsToRemove) {
        itemTimestamps.delete(itemId);
      }

      // Only show message if items were actually cleared
      if (removedItems > 0 || removedMobs > 0) {
        for (const player of world.getPlayers()) {
          player.sendMessage(`✅ Cleared ${removedItems} old dropped items (5+ min) and ${removedMobs} hostile mobs from all dimensions.`);
        }
      }

      countdown = checkInterval;
    }
  }, 20); // Runs every second
});
