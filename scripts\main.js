import { world, system } from "@minecraft/server";

const checkInterval = 10; // Check every 10 seconds
const itemMaxAge = 180; // 5 minutes in seconds
let countdown = checkInterval;

// Map to store item spawn timestamps and player info
const itemTimestamps = new Map();
const itemPlayerMap = new Map(); // Track which player dropped each item

// List of all hostile mob IDs
const hostileMobs = new Set([
  "minecraft:zombie",
  "minecraft:husk",
  "minecraft:drowned",
  "minecraft:skeleton",
  "minecraft:stray",
  "minecraft:spider",
  "minecraft:cave_spider",
  "minecraft:creeper",
  "minecraft:enderman",
  "minecraft:witch",
  "minecraft:vindicator",
  "minecraft:evoker",
  "minecraft:ravager",
  "minecraft:pillager",
  "minecraft:illusioner",
  "minecraft:zombified_piglin",
  "minecraft:blaze",
  "minecraft:ghast",
  "minecraft:magma_cube",
  "minecraft:slime",
  "minecraft:phantom",
  "minecraft:warden",
  "minecraft:shulker",
  "minecraft:guardian",
  "minecraft:elder_guardian",
  "minecraft:hoglin",
  "minecraft:piglin_brute"
]);

const dimensions = ["overworld", "nether", "the_end"];

// Track when items are spawned and check if they're near players (likely dropped by them)
world.afterEvents.entitySpawn.subscribe((event) => {
  if (event.entity.typeId === "minecraft:item") {
    const currentTime = Date.now();
    itemTimestamps.set(event.entity.id, currentTime);

    // Check if item spawned near a player (within 3 blocks)
    const itemLocation = event.entity.location;
    const dimension = event.entity.dimension;

    for (const player of world.getPlayers()) {
      if (player.dimension.id === dimension.id) {
        const playerLocation = player.location;
        const distance = Math.sqrt(
          Math.pow(itemLocation.x - playerLocation.x, 2) +
          Math.pow(itemLocation.y - playerLocation.y, 2) +
          Math.pow(itemLocation.z - playerLocation.z, 2)
        );

        // If item spawned within 3 blocks of a player, assume they dropped it
        if (distance <= 3) {
          itemPlayerMap.set(event.entity.id, player.id);

          // Get item name for the message
          const itemComponent = event.entity.getComponent("minecraft:item");
          const itemStack = itemComponent?.itemStack;
          const itemName = itemStack ? itemStack.typeId.replace("minecraft:", "").replace("_", " ") : "item";

          player.sendMessage(`⏰ ﺎﻬﻃﺎﻘﺘﻟﺍ ﻢﺘﻳ ﻢﻟ ﻥﺇ ﻖﺋﺎﻗﺩ 3 ﺪﻌﺑ ﺎﻬﻓﺬﺣ ﻢﺘﻴﺳ ﺓﺍﺩﻷﺍ.`);
          break; // Only notify the closest player
        }
      }
    }
  }
});

// Clean up timestamps when items are removed
world.afterEvents.entityRemove.subscribe((event) => {
  if (event.removedEntityId && itemTimestamps.has(event.removedEntityId)) {
    itemTimestamps.delete(event.removedEntityId);
    itemPlayerMap.delete(event.removedEntityId);
  }
});

world.afterEvents.worldInitialize.subscribe(() => {
  //console.warn("✅ ClearLag system started - Items older than 5 minutes will be cleared every 10 seconds");

  system.runInterval(() => {
    countdown--;

    // Check and clear old items every 10 seconds
    if (countdown <= 0) {
      let removedItems = 0;
      let removedMobs = 0;
      const currentTime = Date.now();
      const itemsToRemove = [];

      for (const dimName of dimensions) {
        const dimension = world.getDimension(dimName);

        for (const entity of dimension.getEntities()) {
          if (entity.typeId === "minecraft:item") {
            const spawnTime = itemTimestamps.get(entity.id);

            // If we don't have a timestamp (item existed before addon loaded) or item is older than 5 minutes
            if (!spawnTime || (currentTime - spawnTime) >= (itemMaxAge * 1000)) {
              entity.remove();
              removedItems++;
              itemsToRemove.push(entity.id);
            }
          } else if (hostileMobs.has(entity.typeId)) {
            entity.remove();
            removedMobs++;
          }
        }
      }

      // Clean up timestamps and player mappings for removed items
      for (const itemId of itemsToRemove) {
        itemTimestamps.delete(itemId);
        itemPlayerMap.delete(itemId);
      }

      // Only show message if items were actually cleared
      // if (removedItems > 0 || removedMobs > 0) {
      //   for (const player of world.getPlayers()) {
      //     player.sendMessage(`✅ Cleared ${removedItems} old dropped items (5+ min) and ${removedMobs} hostile mobs from all dimensions.`);
      //   }
      // }

      countdown = checkInterval;
    }
  }, 20); // Runs every second
});
